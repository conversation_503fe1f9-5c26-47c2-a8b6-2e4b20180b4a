<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Actions\Products\GetRebateEstimateByProductOffer;
use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Enums\ProductStockStatus;
use App\Modules\Order\Data\PreviouslyOrderedItem;
use Brick\Money\Money;
use Carbon\Carbon;

use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class PreviouslyPurchasedItemData extends Data
{
    public function __construct(
        public readonly string $productOfferId,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $unitPrice,
        public readonly int $lastOrderedQuantity,
        public readonly string $lastOrderedAt,
        public readonly int $orderCount,
        public readonly ProductData $product,
        public readonly VendorData $vendor,
        public readonly string $stockStatus,
        public readonly int $increments,
        public readonly bool $isPurchasable,
    ) {}

    public static function fromQueryResult(object $queryResult, string $clinicId, ?\App\Models\Clinic $clinic = null, ?\Illuminate\Support\Collection $productOffers = null): self
    {
        $clinic ??= \App\Models\Clinic::with('account.gpo')->find($clinicId);
        $gpoId = $clinic?->account?->gpo?->id;

        if ($productOffers === null) {
            $productOffers = \App\Models\ProductOffer::query()
                ->with(['vendor', 'clinics'])
                ->withClinic($clinicId)
                ->withVendorConnected([$clinicId])
                ->where('product_id', $queryResult->product_id)
                ->when($gpoId, fn($query) => $query->withExistsGpo($gpoId))
                ->get();
        }

        $getRebateEstimateAction = app(GetRebateEstimateByProductOffer::class);
        $lastOrderedAt = Carbon::parse($queryResult->last_ordered_at);
        $stockStatus = ProductStockStatus::from($queryResult->stock_status);

        // Batch fetch rebate estimates for all offers to avoid N+1 queries
        // Performance improvement: Instead of calling getRebateEstimateAction->handle() for each offer
        // (which could result in 100+ individual queries for 20 items with 5 offers each),
        // we now fetch all rebate estimates in a single batch query with intelligent caching
        $offerIds = $productOffers->pluck('id')->toArray();
        $rebateEstimatesByOfferId = $getRebateEstimateAction->handleBatch($clinicId, $offerIds);

        $offers = $productOffers->map(function ($offer) use ($queryResult, $lastOrderedAt, $rebateEstimatesByOfferId) {
            $rebateEstimate = $rebateEstimatesByOfferId->get($offer->id);
            $clinicPrice = $offer->clinics->first()?->pivot?->price;

            return [
                'id' => $offer->id,
                'vendor' => [
                    'id' => $offer->vendor->id,
                    'name' => $offer->vendor->name,
                    'imageUrl' => $offer->vendor->imageUrl,
                ],
                'name' => $offer->name ?: $queryResult->product_name,
                'vendorSku' => $offer->vendor_sku,
                'price' => ! is_null($offer->price) ? Money::ofMinor($offer->price, 'USD')->getAmount() : null,
                'clinicPrice' => ! is_null($clinicPrice) ? Money::ofMinor($clinicPrice, 'USD')->getAmount() : null,
                'stockStatus' => $offer->stock_status->value,
                'lastOrderedAt' => $offer->id === $queryResult->product_offer_id ? $lastOrderedAt->toIso8601String() : null,
                'lastOrderedQuantity' => $offer->id === $queryResult->product_offer_id ? $queryResult->total_quantity : null,
                'isPurchasable' => $offer->is_purchasable,
                'increments' => $offer->increments,
                'isRecommended' => $offer->is_recommended ?? false,
                'rebatePercent' => $rebateEstimate?->current_rebate_percent,
            ];
        })->toArray();

        return self::from([
            'productOfferId' => $queryResult->product_offer_id,
            'unitPrice' => $queryResult->price,
            'lastOrderedQuantity' => $queryResult->total_quantity,
            'lastOrderedAt' => $lastOrderedAt->toIso8601String(),
            'orderCount' => $queryResult->order_count,
            'product' => ProductData::from([
                'id' => $queryResult->product_id,
                'name' => $queryResult->product_name,
                'imageUrl' => $queryResult->image_url ?? '',
                'offers' => $offers,
            ]),
            'vendor' => VendorData::from([
                'id' => $queryResult->vendor_id,
                'name' => $queryResult->vendor_name,
                'imageUrl' => $queryResult->vendor_image_path ? asset("storage/{$queryResult->vendor_image_path}") : '',
            ]),
            'stockStatus' => $stockStatus->value,
            'increments' => $queryResult->increments,
            'isPurchasable' => $queryResult->deactivated_at === null && $stockStatus !== ProductStockStatus::Discontinued,
        ]);
    }

    public static function fromPreviouslyOrderedItem(PreviouslyOrderedItem $item, string $clinicId, ?\App\Models\Clinic $clinic = null, ?\Illuminate\Support\Collection $productOffers = null): self
    {
        $clinic ??= \App\Models\Clinic::with('account.gpo')->find($clinicId);
        $gpoId = $clinic?->account?->gpo?->id;

        // Use pre-fetched offers if provided, otherwise query for them
        if ($productOffers === null) {
            $productOffers = \App\Models\ProductOffer::query()
                ->with(['vendor', 'clinics'])
                ->withClinic($clinicId)
                ->withVendorConnected([$clinicId])
                ->where('product_id', $item->product_id)
                ->when($gpoId, fn($query) => $query->withExistsGpo($gpoId))
                ->get();
        }

        $getRebateEstimateAction = app(GetRebateEstimateByProductOffer::class);

        // Batch fetch rebate estimates for all offers to avoid N+1 queries
        $offerIds = $productOffers->pluck('id')->toArray();
        $rebateEstimatesByOfferId = $getRebateEstimateAction->handleBatch($clinicId, $offerIds);

        $offers = $productOffers->map(function ($offer) use ($item, $rebateEstimatesByOfferId) {
            $rebateEstimate = $rebateEstimatesByOfferId->get($offer->id);
            $clinicPrice = $offer->clinics->first()?->pivot?->price;

            return [
                'id' => $offer->id,
                'vendor' => [
                    'id' => $offer->vendor->id,
                    'name' => $offer->vendor->name,
                    'imageUrl' => $offer->vendor->imageUrl,
                ],
                'name' => $offer->name ?: $item->product_name,
                'vendorSku' => $offer->vendor_sku,
                'price' => ! is_null($offer->price) ? Money::ofMinor($offer->price, 'USD')->getAmount() : null,
                'clinicPrice' => ! is_null($clinicPrice) ? Money::ofMinor($clinicPrice, 'USD')->getAmount() : null,
                'stockStatus' => $offer->stock_status->value,
                'lastOrderedAt' => $offer->id === $item->product_offer_id ? $item->last_ordered_at->toIso8601String() : null,
                'lastOrderedQuantity' => $offer->id === $item->product_offer_id ? $item->quantity : null,
                'isPurchasable' => $offer->is_purchasable,
                'increments' => $offer->increments,
                'isRecommended' => $offer->is_recommended ?? false,
                'rebatePercent' => $rebateEstimate?->current_rebate_percent,
            ];
        })->toArray();

        return self::from([
            'productOfferId' => $item->product_offer_id,
            'unitPrice' => $item->price,
            'lastOrderedQuantity' => $item->quantity,
            'lastOrderedAt' => $item->last_ordered_at->toIso8601String(),
            'orderCount' => $item->order_count,
            'product' => ProductData::from([
                'id' => $item->product_id,
                'name' => $item->product_name,
                'imageUrl' => $item->image_url,
                'offers' => $offers,
            ]),
            'vendor' => VendorData::from([
                'id' => $item->vendor_id,
                'name' => $item->vendor_name,
                'imageUrl' => $item->vendor_image_path ? asset("storage/{$item->vendor_image_path}") : '',
            ]),
            'stockStatus' => $item->stock_status->value,
            'increments' => $item->increments,
            'isPurchasable' => $item->is_purchasable,
        ]);
    }
}
