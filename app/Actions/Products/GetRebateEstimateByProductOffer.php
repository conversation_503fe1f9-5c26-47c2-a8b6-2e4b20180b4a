<?php

declare(strict_types=1);

namespace App\Actions\Products;

use App\Models\Clinic;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\RebateEstimate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

final class GetRebateEstimateByProductOffer
{
    public function handle(string $clinicId, string $productOfferId): ?RebateEstimate
    {
        $clinic = Clinic::with(['account'])->find($clinicId);

        if (! $clinic || ! $clinic->account) {
            return null;
        }

        $gpoId = $clinic->account->gpo_account_id;

        if (is_null($gpoId)) {
            return null;
        }

        $today = Carbon::today();

        return Cache::flexible(
            "estimate-rebate:{$clinic->id}:{$gpoId}:{$productOfferId}",
            [30, 60],
            fn () => RebateEstimate::query()
                ->with(['promotion', 'promotion.vendor'])
                ->where('clinic_id', $clinic->id)
                ->whereHas('promotion', function (Builder $query) use ($productOfferId, $gpoId, $today) {
                    $query
                        ->whereHas('productOffers', function (Builder $query) use ($productOfferId) {
                            $query->where('product_offer_id', $productOfferId);
                        })
                        ->where('promotionable_type', GpoAccount::class)
                        ->where('promotionable_id', $gpoId)
                        ->where('type', PromotionType::Rebate)
                        ->where('status', PromotionStatus::Active)
                        ->where('started_at', '<=', $today)
                        ->where('ended_at', '>=', $today);
                })->first()
        );
    }

    /**
     * Get rebate estimates for multiple product offers in a single batch query.
     * Returns a collection keyed by product offer ID.
     */
    public function handleBatch(string $clinicId, array $productOfferIds): Collection
    {
        if (empty($productOfferIds)) {
            return collect();
        }

        $clinic = Clinic::with(['account'])->find($clinicId);

        if (! $clinic || ! $clinic->account) {
            return collect();
        }

        $gpoId = $clinic->account->gpo_account_id;

        if (is_null($gpoId)) {
            return collect();
        }

        $today = Carbon::today();
        $results = collect();
        $uncachedOfferIds = [];

        // Check cache for each product offer ID
        foreach ($productOfferIds as $productOfferId) {
            $cacheKey = "estimate-rebate:{$clinic->id}:{$gpoId}:{$productOfferId}";
            $cached = Cache::get($cacheKey);

            if ($cached !== null) {
                $results->put($productOfferId, $cached);
            } else {
                $uncachedOfferIds[] = $productOfferId;
            }
        }

        // Batch fetch uncached rebate estimates
        if (! empty($uncachedOfferIds)) {
            $batchResults = RebateEstimate::query()
                ->with(['promotion', 'promotion.vendor', 'promotion.productOffers'])
                ->where('clinic_id', $clinic->id)
                ->whereHas('promotion', function (Builder $query) use ($uncachedOfferIds, $gpoId, $today) {
                    $query
                        ->whereHas('productOffers', function (Builder $query) use ($uncachedOfferIds) {
                            $query->whereIn('product_offer_id', $uncachedOfferIds);
                        })
                        ->where('promotionable_type', GpoAccount::class)
                        ->where('promotionable_id', $gpoId)
                        ->where('type', PromotionType::Rebate)
                        ->where('status', PromotionStatus::Active)
                        ->where('started_at', '<=', $today)
                        ->where('ended_at', '>=', $today);
                })
                ->get();

            // Map results by product offer ID and cache them
            $estimatesByOfferId = $batchResults->flatMap(function ($estimate) {
                return $estimate->promotion->productOffers->mapWithKeys(function ($offer) use ($estimate) {
                    return [$offer->id => $estimate];
                });
            });

            foreach ($uncachedOfferIds as $productOfferId) {
                $estimate = $estimatesByOfferId->get($productOfferId);
                $cacheKey = "estimate-rebate:{$clinic->id}:{$gpoId}:{$productOfferId}";

                // Cache with flexible TTL (30-60 seconds)
                Cache::put($cacheKey, $estimate, now()->addSeconds(rand(30, 60)));

                $results->put($productOfferId, $estimate);
            }
        }

        return $results;
    }
}
