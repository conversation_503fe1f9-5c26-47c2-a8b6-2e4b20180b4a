<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Modules\Order\Data\PreviouslyPurchasedItemData;
use App\Modules\Order\Queries\PreviouslyOrderedItemsQuery;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\LaravelData\PaginatedDataCollection;

final class PreviouslyPurchasedController extends Controller
{
    /**
     * Get previously purchased items for a clinic with pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $paginatedResults = PreviouslyOrderedItemsQuery::for($request->clinicId())->jsonPaginate();
        $clinicId = $request->clinicId();

        // Load clinic once to avoid N+1 query problem
        $clinic = \App\Models\Clinic::with('account.gpo')->find($clinicId);
        $gpoId = $clinic?->account?->gpo?->id;

        // Pre-fetch all ProductOffers for all products to avoid N+1 queries
        $productIds = $paginatedResults->getCollection()->pluck('product_id')->unique()->values()->all();

        $allProductOffers = \App\Models\ProductOffer::query()
            ->with(['vendor', 'clinics'])
            ->withClinic($clinicId)
            ->withVendorConnected([$clinicId])
            ->whereIn('product_id', $productIds)
            ->when($gpoId, fn($query) => $query->withExistsGpo($gpoId))
            ->get()
            ->groupBy('product_id');

        // Transform directly from query result to PreviouslyPurchasedItemData in a single pass
        $dataCollection = $paginatedResults->getCollection()->map(function ($queryResult) use ($clinicId, $clinic, $allProductOffers) {
            $productOffers = $allProductOffers->get($queryResult->product_id, collect());
            return PreviouslyPurchasedItemData::fromQueryResult($queryResult, $clinicId, $clinic, $productOffers);
        });

        $paginatedResults->setCollection($dataCollection);

        $data = PreviouslyPurchasedItemData::collect(
            $paginatedResults,
            PaginatedDataCollection::class
        );

        return response()->json($data);
    }
}
