<?php

declare(strict_types=1);

namespace Tests\Feature\Actions;

use App\Actions\Products\GetRebateEstimateByProductOffer;
use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\RebateEstimate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

final class GetRebateEstimateByProductOfferBatchTest extends TestCase
{
    use RefreshDatabase;

    private GetRebateEstimateByProductOffer $action;
    private Clinic $clinic;
    private GpoAccount $gpoAccount;

    protected function setUp(): void
    {
        parent::setUp();

        $this->action = app(GetRebateEstimateByProductOffer::class);
        
        // Create test data
        $this->gpoAccount = GpoAccount::factory()->create();
        $this->clinic = Clinic::factory()->create([
            'account_id' => $this->gpoAccount->account_id,
        ]);
    }

    public function test_batch_method_returns_empty_collection_for_empty_array(): void
    {
        $result = $this->action->handleBatch($this->clinic->id, []);

        $this->assertEmpty($result);
    }

    public function test_batch_method_returns_empty_collection_when_clinic_has_no_gpo(): void
    {
        $clinicWithoutGpo = Clinic::factory()->create();
        $productOffer = ProductOffer::factory()->create();

        $result = $this->action->handleBatch($clinicWithoutGpo->id, [$productOffer->id]);

        $this->assertEmpty($result);
    }

    public function test_batch_method_fetches_rebate_estimates_for_multiple_offers(): void
    {
        // Create product offers
        $productOffer1 = ProductOffer::factory()->create();
        $productOffer2 = ProductOffer::factory()->create();
        $productOffer3 = ProductOffer::factory()->create();

        // Create promotion with rebate estimates
        $promotion = Promotion::factory()->create([
            'promotionable_type' => GpoAccount::class,
            'promotionable_id' => $this->gpoAccount->id,
            'type' => PromotionType::Rebate,
            'status' => PromotionStatus::Active,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
        ]);

        // Attach product offers to promotion
        $promotion->productOffers()->attach([
            $productOffer1->id,
            $productOffer2->id,
        ]);

        // Create rebate estimates
        $rebateEstimate1 = RebateEstimate::factory()->create([
            'clinic_id' => $this->clinic->id,
            'promotion_id' => $promotion->id,
            'current_rebate_percent' => 10.0,
        ]);

        $rebateEstimate2 = RebateEstimate::factory()->create([
            'clinic_id' => $this->clinic->id,
            'promotion_id' => $promotion->id,
            'current_rebate_percent' => 15.0,
        ]);

        // Test batch fetch
        $result = $this->action->handleBatch($this->clinic->id, [
            $productOffer1->id,
            $productOffer2->id,
            $productOffer3->id, // This one has no rebate estimate
        ]);

        // Should return estimates for offers 1 and 2, null for offer 3
        $this->assertCount(3, $result);
        $this->assertNotNull($result->get($productOffer1->id));
        $this->assertNotNull($result->get($productOffer2->id));
        $this->assertNull($result->get($productOffer3->id));

        // Verify the estimates are correct
        $this->assertEquals(10.0, $result->get($productOffer1->id)->current_rebate_percent);
        $this->assertEquals(15.0, $result->get($productOffer2->id)->current_rebate_percent);
    }

    public function test_batch_method_uses_cache_when_available(): void
    {
        $productOffer = ProductOffer::factory()->create();
        
        // Pre-populate cache
        $cachedEstimate = RebateEstimate::factory()->make([
            'current_rebate_percent' => 25.0,
        ]);
        
        $cacheKey = "estimate-rebate:{$this->clinic->id}:{$this->gpoAccount->id}:{$productOffer->id}";
        Cache::put($cacheKey, $cachedEstimate, 60);

        $result = $this->action->handleBatch($this->clinic->id, [$productOffer->id]);

        $this->assertCount(1, $result);
        $this->assertEquals(25.0, $result->get($productOffer->id)->current_rebate_percent);
    }

    public function test_batch_method_caches_newly_fetched_estimates(): void
    {
        $productOffer = ProductOffer::factory()->create();

        // Create promotion and rebate estimate
        $promotion = Promotion::factory()->create([
            'promotionable_type' => GpoAccount::class,
            'promotionable_id' => $this->gpoAccount->id,
            'type' => PromotionType::Rebate,
            'status' => PromotionStatus::Active,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
        ]);

        $promotion->productOffers()->attach($productOffer->id);

        RebateEstimate::factory()->create([
            'clinic_id' => $this->clinic->id,
            'promotion_id' => $promotion->id,
            'current_rebate_percent' => 20.0,
        ]);

        // Clear cache to ensure we're fetching fresh
        Cache::flush();

        $result = $this->action->handleBatch($this->clinic->id, [$productOffer->id]);

        // Verify result
        $this->assertCount(1, $result);
        $this->assertEquals(20.0, $result->get($productOffer->id)->current_rebate_percent);

        // Verify it was cached
        $cacheKey = "estimate-rebate:{$this->clinic->id}:{$this->gpoAccount->id}:{$productOffer->id}";
        $cached = Cache::get($cacheKey);
        $this->assertNotNull($cached);
        $this->assertEquals(20.0, $cached->current_rebate_percent);
    }

    public function test_single_handle_method_still_works(): void
    {
        $productOffer = ProductOffer::factory()->create();

        // Create promotion and rebate estimate
        $promotion = Promotion::factory()->create([
            'promotionable_type' => GpoAccount::class,
            'promotionable_id' => $this->gpoAccount->id,
            'type' => PromotionType::Rebate,
            'status' => PromotionStatus::Active,
            'started_at' => now()->subDay(),
            'ended_at' => now()->addDay(),
        ]);

        $promotion->productOffers()->attach($productOffer->id);

        RebateEstimate::factory()->create([
            'clinic_id' => $this->clinic->id,
            'promotion_id' => $promotion->id,
            'current_rebate_percent' => 30.0,
        ]);

        $result = $this->action->handle($this->clinic->id, $productOffer->id);

        $this->assertNotNull($result);
        $this->assertEquals(30.0, $result->current_rebate_percent);
    }
}
